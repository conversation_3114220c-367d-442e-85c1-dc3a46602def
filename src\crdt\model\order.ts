import type { Product } from "./product";
import type { Table } from "./table";
import type { User } from "./user";

export type OrderItem = {
  /**
   * The productId maps to the ordered product
   */
  productId: Product["id"];
  /**
   * The payed flag marks if this OrderItems was already payed.
   */
  payed: boolean;
  /**
   * The price in cents. This can be used to override the product price and see what the price for this specific item was.
   */
  priceCents: number;
  /**
   * The doneProductionAt marks if this OrderItems was done by the production team (kitchen, bar) at a timestamp.
   */
  doneProductionAt: string | undefined;
  /**
   * The doneServiceAt marks if this OrderItems was done by the service team (waiter) at a timestamp.
   */
  doneServiceAt: string | undefined;
  /**
   * A not can be a modification to an ordered product.
   */
  note?: string;
};

/**
 * An order is a group of projects that was ordered by a user for a table
 */
export type Order = {
  /**
   * The order id is only for internal usage and should not be user facing.
   */
  id: string;
  /**
   * The userId maps to the user who placed the order (a cashier or bartender)
   */
  userId: User["id"];
  /**
   * The tableId maps the othe table that the order was placed to
   */
  tableId: Table["id"];
  /**
   * The order content contains all products that were ordered grouped by productId
   */
  content: {
    productId: Product["id"];
    /**
     * The items contain an array of OrderItems. They basically just resemble a single product.
     * So the quantity or an ordered product can be known by the length of the array.
     * The OrderItem can also contain a note or other modifiers for a product of an order.
     */
    items: OrderItem[];
  }[];
  /**
   * A timestamp that marks the order as fully payed. All the items were marked as payed inside the order.
   */
  payedAt: string | undefined;
  /**
   * A boolean flag that marks if the order has been printed.
   * When an order is created, this flag is false. It gets set to true when the print job was done.
   */
  printed: boolean;
  /**
   * The doneProductionAt marks if this OrderItems was done by the production team (kitchen, bar) at a timestamp.
   */
  doneProductionAt: string | undefined;
  /**
   * The doneServiceAt marks if this OrderItems was done by the service team (waiter) at a timestamp.
   */
  doneServiceAt: string | undefined;
  /**
   * An ISO timestamp of the creation of the order
   */
  createdAt: string;
};
